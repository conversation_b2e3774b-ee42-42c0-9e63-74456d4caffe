#!/usr/bin/env python3
"""
Example usage script with your credentials
This script demonstrates how to use the Rently Invoice Downloader
"""

from rently_invoice_downloader import RentlyInvoiceDownloader
from invoice_analyzer import InvoiceAnalyzer
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Your credentials
USERNAME = "manuel"
PASSWORD = "Vivarent123"
BASE_URL = "https://vivarent.rently.com.ar"

def test_single_invoice():
    """Test analyzing and downloading a single invoice"""
    print("=== Testing Single Invoice Analysis ===")
    
    # Create analyzer
    analyzer = InvoiceAnalyzer(BASE_URL)
    
    # Login
    if not analyzer.login(USERNAME, PASSWORD):
        print("❌ Login failed!")
        return False
    
    print("✅ Login successful!")
    
    # Analyze invoice ID 3
    analysis = analyzer.analyze_invoice_page(3, save_html=True)
    
    if analysis:
        print(f"✅ Analysis successful for invoice 3")
        print(f"   Page title: {analysis['page_title']}")
        print(f"   Potential PDF URLs found: {len(analysis['potential_pdf_urls'])}")
        
        # Test PDF downloads if URLs found
        if analysis['potential_pdf_urls']:
            print("   Testing PDF downloads...")
            for url in analysis['potential_pdf_urls'][:2]:  # Test first 2 URLs
                result = analyzer.test_pdf_download(3, url)
                print(f"   {'✅' if result else '❌'} {url}")
        
        return True
    else:
        print("❌ Analysis failed!")
        return False

def test_bulk_download():
    """Test bulk downloading multiple invoices"""
    print("\n=== Testing Bulk Download ===")
    
    # Create downloader
    downloader = RentlyInvoiceDownloader(BASE_URL)
    
    # Login
    if not downloader.login(USERNAME, PASSWORD):
        print("❌ Login failed!")
        return False
    
    print("✅ Login successful!")
    
    # Download invoices 3-5 (small test range)
    successful, failed = downloader.download_invoice_range(
        start_id=3,
        end_id=5,
        output_dir="test_downloads",
        delay=1.0
    )
    
    print(f"✅ Bulk download completed!")
    print(f"   Successful: {len(successful)}")
    print(f"   Failed: {len(failed)}")
    
    if successful:
        print("   Downloaded files:")
        for invoice_id, filepath in successful:
            print(f"     Invoice {invoice_id}: {filepath}")
    
    if failed:
        print(f"   Failed invoice IDs: {failed}")
    
    return len(successful) > 0

def main():
    """Run the complete test"""
    print("🚀 Starting Rently Invoice Downloader Test")
    print(f"   Username: {USERNAME}")
    print(f"   Base URL: {BASE_URL}")
    print()
    
    # Test single invoice first
    single_success = test_single_invoice()
    
    if single_success:
        # If single test works, try bulk download
        bulk_success = test_bulk_download()
        
        if bulk_success:
            print("\n🎉 All tests passed! The solution is working correctly.")
            print("\nNext steps:")
            print("1. Check the 'test_downloads' folder for downloaded PDFs")
            print("2. Review 'invoice_3_page.html' to understand the page structure")
            print("3. Run bulk downloads with your desired invoice ID range:")
            print(f"   python rently_invoice_downloader.py --username {USERNAME} --password {PASSWORD} --start-id 1 --end-id 50")
        else:
            print("\n⚠️  Single analysis worked but bulk download had issues.")
            print("   Check the logs and try adjusting the PDF detection patterns.")
    else:
        print("\n❌ Initial test failed. Please check:")
        print("1. Internet connection")
        print("2. Credentials are correct")
        print("3. The Rently platform is accessible")
        print("4. Your account has access to invoice ID 3")

if __name__ == "__main__":
    main()
