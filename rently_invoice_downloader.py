#!/usr/bin/env python3
"""
Rently Invoice PDF Downloader

This script downloads PDF invoices from the Rently platform by:
1. Authenticating with username/password
2. Iterating through invoice IDs
3. Downloading PDF files with meaningful names
4. Handling authentication and session management

Usage:
    python rently_invoice_downloader.py --username YOUR_USERNAME --password YOUR_PASSWORD --start-id 3 --end-id 100
"""

import requests
import argparse
import os
import sys
import time
import re
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('rently_downloader.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class RentlyInvoiceDownloader:
    def __init__(self, base_url="https://vivarent.rently.com.ar"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.authenticated = False
        
    def login(self, username, password):
        """Authenticate with the Rently platform"""
        logger.info("Starting authentication process...")
        
        # First, get the login page to extract the anti-forgery token
        login_url = urljoin(self.base_url, "/Account/Login")
        
        try:
            response = self.session.get(login_url)
            response.raise_for_status()
            
            # Parse the login form to get the anti-forgery token
            soup = BeautifulSoup(response.text, 'html.parser')
            token_input = soup.find('input', {'name': '__RequestVerificationToken'})
            
            if not token_input:
                logger.error("Could not find anti-forgery token in login form")
                return False
                
            token = token_input.get('value')
            logger.info("Retrieved anti-forgery token")
            
            # Prepare login data
            login_data = {
                '__RequestVerificationToken': token,
                'UserName': username,
                'Password': password,
                'RememberMe': 'false'
            }
            
            # Submit login form
            response = self.session.post(login_url, data=login_data, allow_redirects=True)
            response.raise_for_status()
            
            # Check if login was successful by looking for redirect or success indicators
            if "Account/Login" not in response.url and response.status_code == 200:
                logger.info("Authentication successful")
                self.authenticated = True
                return True
            else:
                logger.error("Authentication failed - still on login page")
                return False
                
        except requests.RequestException as e:
            logger.error(f"Error during authentication: {e}")
            return False
    
    def get_invoice_details(self, invoice_id):
        """Get invoice details page and extract PDF download information"""
        if not self.authenticated:
            logger.error("Not authenticated. Please login first.")
            return None
            
        invoice_url = urljoin(self.base_url, f"/Invoice/Details/{invoice_id}")
        
        try:
            logger.info(f"Fetching invoice details for ID: {invoice_id}")
            response = self.session.get(invoice_url)
            response.raise_for_status()
            
            # Check if we were redirected to login (session expired)
            if "Account/Login" in response.url:
                logger.error("Session expired. Please re-authenticate.")
                self.authenticated = False
                return None
            
            return response.text
            
        except requests.RequestException as e:
            logger.error(f"Error fetching invoice {invoice_id}: {e}")
            return None
    
    def extract_pdf_download_url(self, invoice_html, invoice_id):
        """Extract PDF download URL from invoice details page"""
        soup = BeautifulSoup(invoice_html, 'html.parser')
        
        # Look for common PDF download patterns
        pdf_links = []
        
        # Pattern 1: Direct PDF links
        for link in soup.find_all('a', href=True):
            href = link.get('href')
            if href and ('.pdf' in href.lower() or 'pdf' in href.lower()):
                pdf_links.append(urljoin(self.base_url, href))
        
        # Pattern 2: Download buttons or links with specific text
        download_keywords = ['download', 'descargar', 'pdf', 'print', 'imprimir']
        for link in soup.find_all('a', href=True):
            text = link.get_text().lower().strip()
            if any(keyword in text for keyword in download_keywords):
                href = link.get('href')
                if href:
                    pdf_links.append(urljoin(self.base_url, href))
        
        # Pattern 3: Form submissions that might generate PDFs
        for form in soup.find_all('form'):
            action = form.get('action', '')
            if 'pdf' in action.lower() or 'download' in action.lower():
                pdf_links.append(urljoin(self.base_url, action))
        
        # Pattern 4: JavaScript-based downloads (look for common patterns)
        scripts = soup.find_all('script')
        for script in scripts:
            if script.string:
                # Look for PDF-related JavaScript functions
                if 'pdf' in script.string.lower() or 'download' in script.string.lower():
                    # Try to extract URLs from JavaScript
                    url_pattern = r'["\']([^"\']*(?:pdf|download)[^"\']*)["\']'
                    matches = re.findall(url_pattern, script.string, re.IGNORECASE)
                    for match in matches:
                        if match.startswith('/') or match.startswith('http'):
                            pdf_links.append(urljoin(self.base_url, match))
        
        # Remove duplicates and filter valid URLs
        unique_links = list(set(pdf_links))
        valid_links = [link for link in unique_links if self.is_valid_pdf_url(link)]
        
        logger.info(f"Found {len(valid_links)} potential PDF download URLs for invoice {invoice_id}")
        return valid_links
    
    def is_valid_pdf_url(self, url):
        """Check if URL looks like a valid PDF download URL"""
        if not url:
            return False
        
        # Basic URL validation
        try:
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                return False
        except:
            return False
        
        # Check for PDF-related patterns
        pdf_indicators = ['.pdf', 'pdf', 'download', 'print', 'export']
        url_lower = url.lower()
        
        return any(indicator in url_lower for indicator in pdf_indicators)
    
    def download_pdf(self, pdf_url, invoice_id, output_dir="downloads"):
        """Download PDF file"""
        try:
            logger.info(f"Downloading PDF for invoice {invoice_id} from: {pdf_url}")
            
            # Create output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)
            
            response = self.session.get(pdf_url, stream=True)
            response.raise_for_status()
            
            # Check if response is actually a PDF
            content_type = response.headers.get('content-type', '').lower()
            if 'pdf' not in content_type and not pdf_url.lower().endswith('.pdf'):
                # Try to determine if it's a PDF by checking the first few bytes
                first_bytes = response.content[:10]
                if not first_bytes.startswith(b'%PDF'):
                    logger.warning(f"Response for invoice {invoice_id} doesn't appear to be a PDF")
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"invoice_{invoice_id}_{timestamp}.pdf"
            filepath = os.path.join(output_dir, filename)
            
            # Save the file
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filepath)
            logger.info(f"Successfully downloaded invoice {invoice_id} to {filepath} ({file_size} bytes)")
            return filepath
            
        except requests.RequestException as e:
            logger.error(f"Error downloading PDF for invoice {invoice_id}: {e}")
            return None
        except IOError as e:
            logger.error(f"Error saving PDF for invoice {invoice_id}: {e}")
            return None
    
    def download_invoice_range(self, start_id, end_id, output_dir="downloads", delay=1):
        """Download invoices for a range of IDs"""
        if not self.authenticated:
            logger.error("Not authenticated. Please login first.")
            return
        
        successful_downloads = []
        failed_downloads = []
        
        logger.info(f"Starting download of invoices from ID {start_id} to {end_id}")
        
        for invoice_id in range(start_id, end_id + 1):
            try:
                # Get invoice details page
                invoice_html = self.get_invoice_details(invoice_id)
                if not invoice_html:
                    logger.warning(f"Could not fetch details for invoice {invoice_id}")
                    failed_downloads.append(invoice_id)
                    continue
                
                # Extract PDF download URLs
                pdf_urls = self.extract_pdf_download_url(invoice_html, invoice_id)
                
                if not pdf_urls:
                    logger.warning(f"No PDF download URLs found for invoice {invoice_id}")
                    failed_downloads.append(invoice_id)
                    continue
                
                # Try to download from the first valid URL
                downloaded = False
                for pdf_url in pdf_urls:
                    filepath = self.download_pdf(pdf_url, invoice_id, output_dir)
                    if filepath:
                        successful_downloads.append((invoice_id, filepath))
                        downloaded = True
                        break
                
                if not downloaded:
                    failed_downloads.append(invoice_id)
                
                # Add delay between requests to be respectful
                if delay > 0:
                    time.sleep(delay)
                    
            except Exception as e:
                logger.error(f"Unexpected error processing invoice {invoice_id}: {e}")
                failed_downloads.append(invoice_id)
        
        # Summary
        logger.info(f"Download completed. Successful: {len(successful_downloads)}, Failed: {len(failed_downloads)}")
        if failed_downloads:
            logger.info(f"Failed invoice IDs: {failed_downloads}")
        
        return successful_downloads, failed_downloads


def main():
    parser = argparse.ArgumentParser(description='Download PDF invoices from Rently platform')
    parser.add_argument('--username', required=True, help='Rently username')
    parser.add_argument('--password', required=True, help='Rently password')
    parser.add_argument('--start-id', type=int, default=3, help='Starting invoice ID (default: 3)')
    parser.add_argument('--end-id', type=int, default=10, help='Ending invoice ID (default: 10)')
    parser.add_argument('--output-dir', default='downloads', help='Output directory for PDFs (default: downloads)')
    parser.add_argument('--delay', type=float, default=1.0, help='Delay between requests in seconds (default: 1.0)')
    parser.add_argument('--base-url', default='https://vivarent.rently.com.ar', help='Base URL of Rently instance')
    
    args = parser.parse_args()
    
    # Create downloader instance
    downloader = RentlyInvoiceDownloader(args.base_url)
    
    # Authenticate
    if not downloader.login(args.username, args.password):
        logger.error("Authentication failed. Please check your credentials.")
        sys.exit(1)
    
    # Download invoices
    successful, failed = downloader.download_invoice_range(
        args.start_id, 
        args.end_id, 
        args.output_dir, 
        args.delay
    )
    
    print(f"\nDownload Summary:")
    print(f"Successful downloads: {len(successful)}")
    print(f"Failed downloads: {len(failed)}")
    
    if successful:
        print("\nSuccessful downloads:")
        for invoice_id, filepath in successful:
            print(f"  Invoice {invoice_id}: {filepath}")
    
    if failed:
        print(f"\nFailed invoice IDs: {failed}")


if __name__ == "__main__":
    main()
