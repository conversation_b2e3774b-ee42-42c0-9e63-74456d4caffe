#!/bin/bash

# Rently Invoice Downloader - Easy Run Script
# This script provides an easy way to run the invoice downloader with common configurations

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Python dependencies are installed
check_dependencies() {
    print_info "Checking Python dependencies..."
    
    if ! python3 -c "import requests, bs4" 2>/dev/null; then
        print_warning "Dependencies not found. Installing..."
        pip3 install -r requirements.txt
        print_success "Dependencies installed successfully"
    else
        print_success "All dependencies are installed"
    fi
}

# Function to run the analyzer
run_analyzer() {
    local username="$1"
    local password="$2"
    local invoice_id="$3"
    local base_url="${4:-https://vivarent.rently.com.ar}"
    
    print_info "Analyzing invoice $invoice_id..."
    
    python3 invoice_analyzer.py \
        --username "$username" \
        --password "$password" \
        --invoice-id "$invoice_id" \
        --save-html \
        --test-downloads \
        --output-json "analysis_invoice_${invoice_id}.json" \
        --base-url "$base_url"
    
    if [ $? -eq 0 ]; then
        print_success "Analysis completed successfully"
        print_info "Results saved to analysis_invoice_${invoice_id}.json"
        print_info "HTML saved to invoice_${invoice_id}_page.html"
    else
        print_error "Analysis failed"
        return 1
    fi
}

# Function to run the bulk downloader
run_downloader() {
    local username="$1"
    local password="$2"
    local start_id="$3"
    local end_id="$4"
    local output_dir="${5:-downloads}"
    local base_url="${6:-https://vivarent.rently.com.ar}"
    local delay="${7:-1.0}"
    
    print_info "Starting bulk download from invoice $start_id to $end_id..."
    
    # Create output directory
    mkdir -p "$output_dir"
    
    python3 rently_invoice_downloader.py \
        --username "$username" \
        --password "$password" \
        --start-id "$start_id" \
        --end-id "$end_id" \
        --output-dir "$output_dir" \
        --delay "$delay" \
        --base-url "$base_url"
    
    if [ $? -eq 0 ]; then
        print_success "Bulk download completed successfully"
        print_info "Files saved to $output_dir/"
    else
        print_error "Bulk download failed"
        return 1
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  analyze    - Analyze a single invoice page"
    echo "  download   - Download a range of invoices"
    echo "  help       - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 analyze username password 3"
    echo "  $0 download username password 1 10"
    echo "  $0 download username password 1 10 my_downloads https://custom.rently.com 2.0"
    echo ""
    echo "Environment Variables:"
    echo "  RENTLY_USERNAME - Default username"
    echo "  RENTLY_PASSWORD - Default password"
    echo "  RENTLY_BASE_URL - Default base URL"
}

# Main script logic
main() {
    local command="$1"
    
    case "$command" in
        "analyze")
            check_dependencies
            
            local username="${2:-$RENTLY_USERNAME}"
            local password="${3:-$RENTLY_PASSWORD}"
            local invoice_id="$4"
            local base_url="${5:-${RENTLY_BASE_URL:-https://vivarent.rently.com.ar}}"
            
            if [ -z "$username" ] || [ -z "$password" ] || [ -z "$invoice_id" ]; then
                print_error "Missing required parameters for analyze command"
                echo "Usage: $0 analyze <username> <password> <invoice_id> [base_url]"
                exit 1
            fi
            
            run_analyzer "$username" "$password" "$invoice_id" "$base_url"
            ;;
            
        "download")
            check_dependencies
            
            local username="${2:-$RENTLY_USERNAME}"
            local password="${3:-$RENTLY_PASSWORD}"
            local start_id="$4"
            local end_id="$5"
            local output_dir="${6:-downloads}"
            local base_url="${7:-${RENTLY_BASE_URL:-https://vivarent.rently.com.ar}}"
            local delay="${8:-1.0}"
            
            if [ -z "$username" ] || [ -z "$password" ] || [ -z "$start_id" ] || [ -z "$end_id" ]; then
                print_error "Missing required parameters for download command"
                echo "Usage: $0 download <username> <password> <start_id> <end_id> [output_dir] [base_url] [delay]"
                exit 1
            fi
            
            run_downloader "$username" "$password" "$start_id" "$end_id" "$output_dir" "$base_url" "$delay"
            ;;
            
        "help"|"--help"|"-h"|"")
            show_usage
            ;;
            
        *)
            print_error "Unknown command: $command"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
