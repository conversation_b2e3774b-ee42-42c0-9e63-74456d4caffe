<!DOCTYPE html>
<html lang="it" translate="no">
<head>
    

<meta charset="utf-8" />
<title>Dettagli Fattura #3  | Rently</title>

<link href="/Images/AppIcons/icon.png" rel="shortcut icon" type="image/x-icon" />
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0" />

<link rel="apple-touch-icon" sizes="57x57" href="/Images/AppIcons/icon-57x57.png" />
<link rel="apple-touch-icon" sizes="72x72" href="/Images/AppIcons/icon-72x72.png" />
<link rel="apple-touch-icon" sizes="114x114" href="/Images/AppIcons/icon-114x114.png" />
<link rel="apple-touch-icon" sizes="144x144" href="/Images/AppIcons/icon-144x144.png" />
<link rel="apple-touch-icon" sizes="60×60" href="/Images/AppIcons/icon-60%c3%9760.png" />
<link rel="apple-touch-icon" sizes="76×76" href="/Images/AppIcons/icon-76%c3%9776.png" />
<link rel="apple-touch-icon" sizes="120×120" href="/Images/AppIcons/icon-120%c3%97120.png" />
<link rel="apple-touch-icon" sizes="152×152" href="/Images/AppIcons/icon-152%c3%97152.png" />
<link rel="apple-touch-icon" sizes="180×180" href="/Images/AppIcons/icon-180%c3%97180.png" />
<link rel="icon" sizes="192×192" href="/Images/AppIcons/icon-192x192.png" />
<link rel="icon" sizes="128×128" href="/Images/AppIcons/icon.png" />
<link rel="manifest" href="/manifest.json" />

<meta property="og:title" content="vivarent | Rently" />
<meta property="og:description" content="Dettagli Fattura #3 " />
<meta property="og:url" content="https://vivarent.rently.com.ar/Invoice/Details/3" />
<meta property="og:image" content="https://storerentlyprod002.blob.core.windows.net/vivarent/CustomerLogo/vivarent.png" />


    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;400;500;700&display=swap" rel="stylesheet">

    <link href="/theme/site" rel="stylesheet"/>

    <link href="/bundles/css?v=cHX-OveeY82cckYt99eghNNvqpkkyyYx57uCSHkrHTc1" rel="stylesheet"/>

    <link href="/bundles/themes/base/css?v=HUovVmBHb3ZO7Vhbv65907sYJ5sKlYUvpki4GkLk2Y81" rel="stylesheet"/>

    <script src="/bundles/jquery?v=EONoKLGwlSYvUKSzwS9V-y4OJBOrE6nBRRD27xYNACM1"></script>

    <script src="/bundles/jqueryval?v=ky4rixNb-pC0Ns1enlZ-DbXaHee3Tn59mgUnrJZnXeI1"></script>

    <script src="/bundles/jqueryui?v=bJpLGu463piw91aE2190EVQG877ff__td_HMqkVSo9o1"></script>

    <script src="/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1"></script>


    <link href="/Scripts/toastr/toastr.min.css" rel="stylesheet"/>

    <link href="/Scripts/dropzone/basic.min.css" rel="stylesheet"/>

    <link href="/Scripts/dropzone/dropzone.min.css" rel="stylesheet"/>




    <script src="/bundles/globalize-it?v=54M4MwowXz0xBBAfVTnW8IuASUCseYHltRTI9O1T4YU1"></script>

    <script src="/bundles/mvc-grid-it?v=f95_J9blCddAHLyfGYattOmVAYOP51ZeshjEloHZ7mE1"></script>


    <script src="/home/<USER>"></script>

    <script src="/Scripts/dropzone/dropzone.min.js"></script>

    <script src="/Scripts/toastr/toastr.min.js"></script>

    <script src="/Scripts/globalize/globalize-validation.js"></script>

    <script src="/Scripts/templates.js"></script>

    <script src="/bundles/rently?v=ijl5jnNTsj8RpXhEQ_IabKTclR9cFs-yCkgvJWoaIoE1"></script>

    

    
    <link href="/content/site-new.css" rel="stylesheet"/>

    <link href="/content/Site-mobile.css" rel="stylesheet"/>


    <script type="text/javascript" nonce="c74506b51d8845f4903918c1ff3c8879">

        var appInsights = window.appInsights || function (config) { function r(config) { t[config] = function () { var i = arguments; t.queue.push(function () { t[config].apply(t, i) }) } } var t = { config: config }, u = document, e = window, o = "script", s = u.createElement(o), i, f; s.src = config.url || "https://az416426.vo.msecnd.net/scripts/a/ai.0.js"; u.getElementsByTagName(o)[0].parentNode.appendChild(s); try { t.cookie = u.cookie } catch (h) { } for (t.queue = [], i = ["Event", "Exception", "Metric", "PageView", "Trace", "Dependency"]; i.length;) r("track" + i.pop()); return r("setAuthenticatedUserContext"), r("clearAuthenticatedUserContext"), config.disableExceptionTracking || (i = "onerror", r("_" + i), f = e[i], e[i] = function (config, r, u, e, o) { var s = f && f(config, r, u, e, o); return s !== !0 && t["_" + i](config, r, u, e, o), s }), t }({ instrumentationKey: "c0d3e56f-6b79-4bc9-8652-e6a1819d32d1" });
        window.appInsights = appInsights;
        appInsights.queue.push(function () {
            appInsights.context.addTelemetryInitializer(function (envelope) {
                if (envelope.name === Microsoft.ApplicationInsights.Telemetry.RemoteDependencyData.envelopeType && envelope.data.baseData.name.indexOf("signalr")>-1){
                    return false;
                }
            });
        });
        appInsights.trackPageView();
    </script>
</head>
<body>
	

<div class="header">
	<div class="content-wrapper">
		<div class="config-menu float-left" href="#"><span class="material-icons-outlined-28">apps</span></div>
		<div class="admin-menu">
			<h3 class="">Amministrazione</h3>

				<div class="menu-item">
					<a href="/Settings">
						<span class="material-icons-outlined">
							settings
						</span>
						<span>Impostazioni generali</span>
					</a>
				</div>

				<div class="menu-item">
					<a href="/BranchOffice">
						<span class="material-icons-outlined">
							other_houses
						</span>
						<span>Filiali</span>
					</a>
				</div>

				<div class="menu-item">
					<a href="/Additional">
						<span class="material-icons-outlined">
							add_circle_outline
						</span>

						<span>Aggiuntivi</span>
					</a>
				</div>

				<div class="menu-item">
					<a href="/Account">
						<span class="material-icons-outlined">
							account_circle
						</span>
						<span>Utenti</span>
					</a>
				</div>

				<div class="menu-item">
					<a href="/Role">
						<span class="material-icons-outlined">
							lock_open
						</span>
						<span>Ruoli</span>
					</a>
				</div>

				<div class="menu-item">
					<a href="/ServiceTypes">
						<span class="material-icons-outlined">
							build
						</span>
						<span>Servizi</span>
					</a>
				</div>

				<div class="menu-item">
					<a href="/Contract">
						<span class="material-icons-outlined">
							receipt_long
						</span>
						<span>Template di contratto</span>
					</a>
				</div>

				<div class="menu-item">
					<a href="/MailNotifications">
						<span class="material-icons-outlined">
							notifications
						</span>
						<span>Notifiche via mail</span>
					</a>
				</div>

				<div class="menu-item">
					<a href="/ExternalSystem">
						<span class="material-icons-outlined">
							group_add
						</span>
						<span>Sistemi esterni</span>
					</a>
				</div>

				<div class="menu-item">
					<a href="/Cash/Configuration">
						<span class="material-icons-outlined">
							monetization_on
						</span>
						<span>Cassa</span>
					</a>
				</div>

				<div class="menu-item">
					<a href="/BookingOrigin">
						<span class="material-icons-outlined">
							event
						</span>
						<span>Prenotazioni</span>
					</a>
				</div>

				<div class="menu-item">
					<a href="/Tax">
						<span class="material-icons-outlined">
							percent
						</span>
						<span>Tasse</span>
					</a>
				</div>

		</div>

		<div class="float-left">
			<a class="logo-content" href="/Dashboard">
				<div class="site-logo">
				</div>
				<div class="site-title" title="Vivarent - Cassino">Vivarent - Cassino</div>
			</a>
		</div>
		<div class="float-right">

				<div class="float-left">
					<ul class="quick-access">
							<li class="header-icon">
								<a href="javascript:showChangeCurrentBranchOfficeModal()" title="Cambiare la filiale attuale">
									<span class="material-icons-outlined">
										other_houses
									</span>
								</a>
							</li>
					</ul>
				</div>
				<div class="float-left">
					<ul class="quick-access">
							<li class="header-icon">
								<a href="/Booking/Create" title="Nuova Prenotazione">
									<span class="material-icons-outlined">
										event
									</span>
								</a>
							</li>
					</ul>
				</div>
				<div class="dropdown float-left">
					<button class="btn-header" type="button" data-toggle="dropdown">
						<span class="material-icons-outlined">
							menu
						</span>
					</button>
					<ul class="dropdown-menu header-menu">

							<li>
								<a href="javascript:changeLocationPopUp({ onlyInCurrentBranchOffice: true })"
								   class="">
									<span class="material-icons-outlined headerdropdown">
										swap_horiz
									</span>
									Cambia posizione
								</a>
							</li>



							<li class="">
								<a href="/Customer/Create"
								   class="">
									<span class="material-icons-outlined headerdropdown">
										person_add
									</span>
									Nuovo Cliente
								</a>
							</li>


							<li class="">
								<a href="/Infraction/Create"
								   class="">
									<span class="material-icons-outlined headerdropdown">
										block
									</span>
									Nuova infrazione
								</a>
							</li>


							<li class="">
								<a href="/Service/Create"
								   class="">
									<span class="material-icons-outlined headerdropdown">
										build
									</span>
									Nuovo servizio
								</a>
							</li>


							<li class="">
								<a href="javascript:newCollect(false)"
								   class="">
									<span class="material-icons-outlined headerdropdown">
										attach_money
									</span>
									Raccogliere
								</a>
							</li>


							<li class="">
								<a href="javascript:newExpense()"
								   class="">
									<span class="material-icons-outlined headerdropdown">
										payments
									</span>
									Spesa
								</a>
							</li>

					</ul>
				</div>
				<div class="user-nav">
					<div class="navbar-collapse collapse">

						<ul class="nav navbar-nav">

							<li class="searchBox"><input autocomplete="off" class="searchBox" id="searchBox" name="searchBox" type="text" value="" /></li>

						</ul>
					</div>
				</div>
				<div class="float-right">
					<div class="user-nav">
						<div class="navbar-collapse">
							<ul class="nav navbar-nav">
								<li class="dropdown">
										<a href="#" class="dropdown-toggle profile" data-toggle="dropdown" role="button">
			<div title="" class="profile-initials green small">
		<span>Ma</span>
	</div>

	</a>
    <ul class="dropdown-menu dropdown-menu-right" role="menu" data-original-title="" title="">

        <li class="menu-item"><a href="/Settings/UserSettings">Profilo</a></li>

        <li class="menu-item my-profile"><a href="/Account/Manage">Cambia password</a></li>
        <li>
            <div class="theme-switch-wrapper">
                <label class="theme-switch" for="checkbox">
                    <input type="checkbox" id="checkbox" />
                    <div class="slider round"></div>
                </label>
                <span style="color:var(--light-gray)">Dark Mode</span>
            </div>
        </li> 
        <li class="divider"></li>
        <li class="menu-item ">
            <a href="javascript:document.getElementById('logoutForm').submit()">Disconnetti</a>
<form action="/Account/LogOff" id="logoutForm" method="post"><input name="__RequestVerificationToken" type="hidden" value="2V2p2CtFLKDGDKJCBFOsb5hRF5BqF8s7_k-VtYnIbZqkkFWLKiXkdNnoCCtjJHalbxCNrHawkcYAe7j0hhW5edku3mp-wt8BNZo-YgJLvMs1" /></form>        </li>
    </ul>

<script type="text/javascript" nonce="c74506b51d8845f4903918c1ff3c8879">
    const toggleSwitch = document.querySelector('.theme-switch input[type="checkbox"]');

    function switchTheme(e) {
        if (e.target.checked) {
            document.documentElement.setAttribute('data-theme', 'dark');
        }
        else {
            document.documentElement.setAttribute('data-theme', 'light');
        }
    }

    toggleSwitch.addEventListener('change', switchTheme, false);
    // store user preference for future visits
    function switchTheme(e) {
        if (e.target.checked) {
            document.documentElement.setAttribute('data-theme', 'dark');
            localStorage.setItem('theme', 'dark'); //add this
        }
        else {
            document.documentElement.setAttribute('data-theme', 'light');
            localStorage.setItem('theme', 'light'); //add this
        }
    }
    const currentTheme = localStorage.getItem('theme') ? localStorage.getItem('theme') : null;

    if (currentTheme) {
        document.documentElement.setAttribute('data-theme', currentTheme);

        if (currentTheme === 'dark') {
            toggleSwitch.checked = true;
        }
    }
</script>
								</li>
							</ul>
						</div>
					</div>
				</div>
		</div>
	</div>
</div>
<script nonce="c74506b51d8845f4903918c1ff3c8879">

    $(document).ready(function () {
        if ($(".sub-menu").has("a").length > 0) {
            $(".sub-menu").fadeIn(600).delay(100);
            $(".grid-layout-body").css("margin-top", 35);
            $(".general-layout-body").css("margin-top", 35);
        }

        $(".nav.navbar-nav").children().tooltip();

        $(".config-menu").click(function () {
            $(this).toggleClass("active");
            $(".admin-menu").fadeToggle(200);
            $("div.admin-menu").height($("body").height());

            event.stopPropagation();
        })

        $(document).click(function () {
            $(".config-menu").removeClass("active");
            $(".admin-menu").fadeOut(100);
        });
    });

    function showChangeCurrentBranchOfficeModal() {
        showDialog("changeCurrentBranchOfficeModal", "Cambiare la filiale attuale", null, true, "", {}, function () {
            $.get("/Home/ChangeCurrentBranchOffice", function (data) {
                $("#changeCurrentBranchOfficeModal .modal-body").html(data);
            })
        });
    }
</script>


	
<div class="left-menu">
    <ul class="nav">

        <li><a href="#"><i class="fa fa-chevron-right"></i></a></li>
        <li class="dropdown">
            <a class="dropdown-toggle" href="/Dashboard">
                <span class="material-icons-outlined">home</span>
                <span class="menu-title">Home</span>
            </a>
        </li>



            <li class="dropdown">
                <a data-toggle="dropdown" class="dropdown-toggle" href="#">
                    <span class="material-icons-outlined">event</span>
                    <span class="menu-title">Prenotazioni</span>
                    <i class="fa fa-angle-down"></i>
                    <i class="fa fa-angle-up"></i>
                </a>
                <ul class="dropdown-menu" role="menu">
                    <li class="group-title">Prenotazioni</li>

                        <li><a href="/Booking">Tutte le prenotazioni</a></li>
                        <li><a href="/Booking/CalendarBookings">Calendario di occupazione</a></li>
                        <li><a href="/Booking/WithDebt">Prenotazioni con debito</a></li>
                        <li><a href="/Booking/NoShow">Prenotazioni non presentate</a></li>

                        <li><a href="/Calendar">Calendario</a></li>

                        <li><a href="/BookingFranchise">Depositi cauzionali</a></li>
                        <li><a href="/BookingFranchise/ToReturn">Franchigie da restituire</a></li>
                        <li><a href="/BookingFranchise/Expired">Franchigie da scadere</a></li>

                        <li><a href="/Booking/OccupancyForecast">Previsione di occupazione</a></li>

                </ul>
            </li>






            <li class="dropdown">
                <a class="dropdown-toggle" href="#" data-toggle="dropdown">
                    <span class="material-icons-outlined">directions_car</span>
                    <span class="menu-title">Auto</span>
                    <i class="fa fa-angle-down"></i>
                    <i class="fa fa-angle-up"></i>
                </a>
                <ul class="dropdown-menu" role="menu">
                    <li class="group-title">Auto</li>
                        <li><a href="/Car">Tutte le auto</a></li>
                                            <li><a href="/Model">Modelli</a></li>
                                            <li><a href="/Brand">Marche</a></li>
                                            <li><a href="/Category">Categorie</a></li>
                                            <li><a href="/Car/FleetStatus">Stato della Flotta</a></li>
                                            <li><a href="/InsurancePolicy">Polizze assicurative</a></li>
                                    </ul>
            </li>




            <li class="dropdown">
                <a class="dropdown-toggle" href="#" data-toggle="dropdown">
                    <span class="material-icons-outlined">person_outline</span>
                    <span class="menu-title">Clienti</span>
                    <i class="fa fa-angle-down"></i>
                    <i class="fa fa-angle-up"></i>
                </a>
                <ul class="dropdown-menu" role="menu">

                    <li class="group-title">Clienti</li>

                        <li><a href="/Customer">Tutti i clienti</a></li>
                        <li><a href="/Customer/WithDebt">Clienti con debito</a></li>

                </ul>
            </li>

            <li class="dropdown">
                <a class="dropdown-toggle" href="/Infraction" data-toggle="dropdown">
                    <span class="material-icons-outlined">block</span>
                    <span class="menu-title">Infrattazioni</span>
                    <i class="fa fa-angle-down"></i>
                    <i class="fa fa-angle-up"></i>
                </a>
                <ul class="dropdown-menu" role="menu">
                        <li class="group-title">Infrattazioni</li>
                        <li>
                            <a href="/Infraction ">Tutte le infrazioni</a>

                        </li>
                        <li>
                            <a href="/Infraction/InfractionsToPay ">Infrazioni da pagare</a>
                        </li>
                                            <li>
                            <a href="/InfractionTypes ">Tipi di Infrazione</a>
                        </li>

                </ul>
            </li>

                <li class="dropdown">
                    <a class="dropdown-toggle" href="/Incident" data-toggle="dropdown">
                        <span class="material-icons-outlined">car_crash</span>
                        <span class="menu-title">Incidenti</span>
                        <i class="fa fa-angle-down"></i>
                        <i class="fa fa-angle-up"></i>
                    </a>
                    <ul class="dropdown-menu" role="menu">
                        <li class="group-title">Incidenti</li>
                            <li>
                                <a href="/Incident ">Tutti gli incidenti</a>
                            </li>
                                                    <li>
                                <a href="/IncidentTypes ">Tipo di incidenti</a>
                            </li>
                                                    <li>
                                <a href="/IncidentItemType ">Tipi di oggetti incidenti</a>
                            </li>
                    </ul>
                </li>

                <li class="dropdown">
                    <a class="dropdown-toggle" href="/RentalAgentCommissions" data-toggle="dropdown">
                        <span class="material-icons-outlined">percent</span>
                        <span class="menu-title">Commissioni degli Agenti di Noleggio</span>
                        <i class="fa fa-angle-down"></i>
                        <i class="fa fa-angle-up"></i>
                    </a>
                    <ul class="dropdown-menu" role="menu">
                        <li class="group-title">Commissioni degli Agenti di Noleggio</li>
                            <li>
                                <a href="/RentalAgentCommissionSettings ">Commissioni</a>
                            </li>
                            <li>
                                <a href="/RentalAgentCategories ">Categorie</a>
                            </li>
                        <li>
                            <a href="/RentalAgentReports ">Report</a>
                        </li>
                    </ul>
                </li>



            <li class="dropdown">
                <a class="dropdown-toggle" href="/Service" data-toggle="dropdown">
                    <span class="material-icons-outlined">build</span>
                    <span class="menu-title">Servizi</span>
                    <i class="fa fa-angle-down"></i>
                    <i class="fa fa-angle-up"></i>
                </a>
                <ul class="dropdown-menu" role="menu">
                    <li class="group-title">Servizi</li>
                        <li>
                            <a href="/Service ">Tutti i servizi</a>
                        </li>
                        <li>
                            <a href="/Service/PendingPaymentServices ">In sospeso</a>
                        </li>
                        <li>
                            <a href="/Service/ServicesStatus ">Stato dei Servizi</a>
                        </li>
                                            <li>
                            <a href="/ServiceTypes ">Tipi di Servizi</a>
                        </li>
                                            <li>
                            <a href="/ServiceSchedule/CurrentSchedules ">Calendario dei Servizi</a>
                        </li>
                </ul>
            </li>




            <li class="dropdown">
                <a class="dropdown-toggle" href="#" data-toggle="dropdown">
                    <span class="material-icons-outlined">attach_money</span>
                    <span class="menu-title">Cassetta</span>
                    <i class="fa fa-angle-down"></i>
                    <i class="fa fa-angle-up"></i>
                </a>
                <ul class="dropdown-menu" role="menu">
                    <li class="group-title">Cassetta</li>

                        <li><a href="/Cash/Daily">Cassa giornaliera</a></li>

                        <li><a href="/Cash/Montly">Cassa per periodo</a></li>

                        <li><a href="/Cash/Cashflow">Flusso di cassa</a></li>

                        <li><a href="/Cash/Accounts">Conti</a></li>

                        <li><a href="/ExpenseType">Tipi di Spesa</a></li>

                        <li><a href="/ExpenseCategory">Categorie di Spesa</a></li>

                        <li><a href="/Cash/Conciliation">Conciliazioni</a></li>

                        <li><a href="/Cash/Incomes">Entrate</a></li>
                                            <li><a href="/Expense">Spese</a></li>
                                                                <li><a href="/PurchaseOrder">Tutti gli ordini di acquisto</a></li>
                </ul>
            </li>



            <li class="dropdown">
                <a class="dropdown-toggle" href="#" data-toggle="dropdown">
                    <span class="material-icons-outlined">calculate</span>
                    <span class="menu-title">Tariffa</span>
                    <i class="fa fa-angle-down"></i>
                    <i class="fa fa-angle-up"></i>
                </a>
                <ul class="dropdown-menu" role="menu">
                    <li class="group-title">Tariffa</li>
                        <li><a href="/PriceAgreement">Accordo sui prezzi</a></li>
                                            <li><a href="/BookingTariff">Tariffe</a></li>
                                            <li><a href="/Promotion">Promozioni</a></li>
                                            <li><a href="/CommercialAgreements">Accordi Commerciali</a></li>
                                            <li><a href="/OversoldSetting">Overbooking</a></li>
                                            <li><a href="/LastMinuteBookingSetting">Prenotazioni dell&#39;ultimo minuto</a></li>
                </ul>
            </li>



            <li class="dropdown">
                <a class="dropdown-toggle" href="#" data-toggle="dropdown">
                    <span class="material-icons-outlined">bar_chart</span>
                    <span class="menu-title">Report</span>
                    <i class="fa fa-angle-down"></i>
                    <i class="fa fa-angle-up"></i>
                </a>
                <ul class="dropdown-menu" role="menu">
                    <li class="group-title">Report</li>
                        <li><a href="/Report/ProfitReport">Rapporto sui profitti</a></li>
                        <li><a href="/Report/BookingSalesReport">Rapporto sulle vendite</a></li>
                        <li><a href="/Report/DailyReport">Rapporto quotidiano</a></li>
                        <li><a href="/Report/DynamicReports">Report Dinamici</a></li>
                        <li><a href="/Report">Occupazione</a></li>
                        <li><a href="/Report/DailyOccupation">Ocupacion Diaria</a></li>
                        <li><a href="/Report/MonthlyOccupation">Ocupaci&#243;n Mensual</a></li>
                        <li><a href="/Report/OccupationByCarBranchOffice">Occupazione per Auto e Filiale</a></li>
                        <li><a href="/Report/Expenses">Spese</a></li>
                        <li><a href="/Report/AdditionalsMontly">Extra per Periodo</a></li>
                        <li><a href="/Report/AdditionalsByBranchOffice">Extra per Ufficio</a></li>
                        <li><a href="/Report/MonthlyOverviewReport">Rapporto panoramico mensile</a></li>
                                            <li><a href="/CommissionSettlement">Liquidazione delle commissioni</a></li>
                                    </ul>
            </li>




            <li class="dropdown">
                <a class="dropdown-toggle" href="/Alert">
                    <span class="material-icons-outlined">warning_amber</span>
                    <span class="menu-title">Allarmi</span>
                </a>
            </li>

            <li class="dropdown">
                <a class="dropdown-toggle" href="/MailNotifications/Mails">
                    <span class="material-icons-outlined">email</span>
                    <span class="menu-title">Notifiche via mail</span>
                </a>
            </li>



            <li class="dropdown active">
                <a class="dropdown-toggle" href="#" data-toggle="dropdown">
                    <span class="material-icons-outlined">receipt</span>
                    <span class="menu-title">Documenti</span>
                    <i class="fa fa-angle-down"></i>
                    <i class="fa fa-angle-up"></i>
                </a>
                <ul class="dropdown-menu" role="menu">
                    <li class="group-title">Documenti</li>

                        <li><a href="/Invoice">Tutte le fatture</a></li>
                        <li><a href="/Invoice/IssueBookingInvoices">Emettere fatture di prenotazione</a></li>

                    <li><a href="/Proof">Tutti i documenti</a></li>

                                            <li><a href="/CreditNote">Tutte le note di credito</a></li>

                </ul>
            </li>





    </ul>

</div>

<nav class="mobile-menu mobile-only">
    <ul class="mobile-list">
        <li class="active">
            <a href="/Dashboard"><span class="material-icons">home_app_logo</span>Dashboard</a>
        </li>
        <li>
            <a href="/Booking"><span class="material-icons">event</span>Prenotazioni</a>
        </li>
        <li>
            <a href="/Car"><span class="material-icons">directions_car</span>Auto</a>
        </li>
        <li>
            <a href="/Customer"><span class="material-icons">person</span>Clienti</a>
        </li>
        <div class="dropup">
            <button class="btn-mobile dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <span class="material-icons">more_horiz</span>Di pi&#249;
            </button>
            <ul class="dropdown-menu more-mobile-menu pull-right">
                <li>
                    <a href="/Infraction"><span class="material-icons">block</span>Infrattazioni</a>
                </li>
                <li>
                    <a href="/Service"><span class="material-icons">build</span>Servizi</a>
                </li>
            </ul>
        </div>
    </ul>
</nav>

<script type="text/javascript" nonce="c74506b51d8845f4903918c1ff3c8879">

    $(document).ready(function () {
        $(".left-menu ul li:first").click(function () {
            $(".left-menu").toggleClass("big");
            $(".left-menu ul li:first i").toggleClass("fa-chevron-right fa-chevron-left pull-right");
            $(".main-content").toggleClass("big-menu");
            $(".sub-menu").toggleClass("big-menu");
        });

        $(".left-menu .nav li.dropdown").hover(function () {
            if ($(this).parent().parent().hasClass("big"))
                return;

            $(this).addClass("open");

            var dropdownMenu = $($(this).find("ul.dropdown-menu"));

            if (dropdownMenu.length == 0) return;

            var top = $(this).offset().top - $(window).scrollTop();

            dropdownMenu.css({ 'position': 'fixed', 'z-index': 9999, "top": top + 50 });

            // ¿El menú desplegable no entra entero?
            if (window.innerHeight - top - dropdownMenu.outerHeight() - 45 < 0) {

                var space = window.innerHeight - top - 45;

                // Si da el espacio para 2 subitems, lo recortamos. Sino, lo desplegamos hacia arriba
                if (space > 150)
                    dropdownMenu.css("height", `${space}px`);
                else
                    dropdownMenu.css({ 'position': 'fixed', 'z-index': 9999, "top": top - dropdownMenu.outerHeight() + 100 });
            }

            dropdownMenu.fadeIn(300);

        }, function () {
            if ($(this).parent().parent().hasClass("big"))
                return;

            $(this).find("ul.dropdown-menu").hide();
            $($(this).find("ul.dropdown-menu")).css("height", '');
            $(this).removeClass("open");
        });
    });

    // Bottom navigation Mobile.
    // Añade la clase "active" al icono presionado
    // Activa la etiqueta "a" si también se hace click en la etiqueta "li" que lo contiene evitando toques vacios.

    const menuItems = document.querySelectorAll(".mobile-menu ul li");

    function setActiveElement(index) {
        menuItems.forEach((item, i) => item.classList.toggle("active", i === index));
    }

    function handleClick(event) {
        const clickedElement = event.target.closest("li");
        if (!clickedElement) return;

        const index = Array.from(menuItems).indexOf(clickedElement);
        setActiveElement(index);
        localStorage.setItem("activeElementIndex", index);

        const link = clickedElement.querySelector("a");
        link && link.click();
    }

    // Mantiene la clase "active" al elemento presionado luego de recargar la página.
    document.querySelector(".mobile-menu ul").addEventListener("click", handleClick);

    document.addEventListener("DOMContentLoaded", () => {
        const activeElementIndex = localStorage.getItem("activeElementIndex");
        setActiveElement(parseInt(activeElementIndex));
    });
</script>



	<div id="body" class="general-layout-body" tabindex="200">
		<ul class="sub-menu nav" id="action-menu">
			
    <li class="right" data-shortcut-ctrl="true" data-shortcut-key="&lt;" id="menuBack" title="Indietro (CTRL + &lt;)"><a href="/Invoice"><i class="glyphicon glyphicon-chevron-left"></i><span>Indietro</span></a></li>
    





<li class="" id="btnCancelInvoice"><a href="javascript:cancelInvoice(3);"><i class="fa fa-trash"></i><span>Annullare Fattura</span></a></li>
<li class="" id="btnSyncBalanceInvoice"><a href="javascript:syncBalanceInvoice(3);"><i class="fa fa-refresh"></i><span>Sincronizzare il saldo con ERP</span></a></li>
<li class="" id="btnUpdateStatusInvoice"><a href="javascript:updateStatusInvoice(3);"><i class="fa fa-refresh"></i><span>Aggiorna stato</span></a></li>

<li class="" id="btnAddCreditNoteInvoice"><a href="javascript:addCreditNote(null, null, 3);"><i class="fa fa-file-text-o"></i><span>Crea Nota di Credito</span></a></li>
		</ul>

		<input id="ReturnUrl" name="ReturnUrl" type="hidden" value="" />
		
		<section class="content-wrapper main-content clear-fix">
			



<div class="page-title">
    <h2>
        Dettagli Fattura #3 
        <span class="pills success">Elaborato</span>
    </h2>
</div>


<div id="InvoiceDetailsPage">
    <div class="panel col-lg-8">
        <div class="panel-body">
            <ul class="nav nav-tabs">
                <li class="active"><a data-toggle="tab" href="#invoiceDetail">Dettagli</a></li>
                <li><a data-toggle="tab" href="#status">Stato della fattura</a></li>
                
                <li><a data-toggle="tab" href="#comments" data-lazy-grid="/Comment/InvoiceComments?id=3">Commenti</a></li>
                    <li><a data-toggle="tab" href="#items">Items</a></li>
                                                <li><a data-toggle="tab" href="#files">File</a></li>
            </ul>

            <div class="tab-content">
                <div id="invoiceDetail" class="tab-pane fade in active">
                    <div class="col-lg-7 p-0">
                        <div class="row">
                            <div class="col-xs-8">
                                # <br />
                                Fattura  -  Creato da Rently Support
                            </div>

                            <div class="col-xs-4 column-right">
08/01/2025 21:08:07                            </div>
                        </div>
                        <br />
                        <div class="row row-space">
                            <div class="col-xs-6 col-md-6">
                                <div class="payment-type-title">
                                    <h3 class="invoiceTitle">
                                        Data di Emissione
                                    </h3>
                                </div>
                            </div>
                            <div class="col-xs-6 col-md-6 invoice-issue-date column-right">
                                08/01/2025
                            </div>
                        </div>
                        <div class="row row-space">
                            <div class="col-xs-6 col-md-6">
                                <div class="payment-type-title">
                                    <h3 class="invoiceTitle">
                                        Importo totale
                                    </h3>
                                </div>
                            </div>
                            <div class="col-xs-6 col-md-6 payment-amount column-right">
                                € 1.306,80
                            </div>
                        </div>
                        <div class="row row-space">
                            <div class="col-xs-6 col-md-6">
                                <div class="payment-type-title">
                                    <h3 class="invoiceTitle">
                                        Saldo
                                    </h3>
                                </div>
                            </div>
                            <div class="col-xs-6 col-md-6 payment-amount debt column-right">
                                € 1.306,80
                            </div>
                        </div>


                                                                            <div class="row">
                                <div class="col-xs-12">
                                    <p>Pick up: Cassino Downtown</p>
                                </div>
                            </div>

                    </div>

                    <div class="col-lg-5 space-mail">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="payment-type-title">
                                        <h3 style="margin-top:0">Mail non inviato</h3>

                                </div>
                            </div>
                        </div>
                        <br />
                                                                                                    <div class="row">
                                <div class="col-xs-12">
                                    <p>Fornitore ID in ERP : 1</p>
                                </div>
                            </div>
                                                    <div class="row">
                                <div class="col-xs-12">
                                    <p>Tipo di Fattura : Factura</p>
                                </div>
                            </div>
                                                                    </div>
                </div>
                <div id="status" class="tab-pane fade">
                        <div class="col-lg-6 p-0">
                                <div class="row">
                                    <div class="col-xs-8">
                                        <h2 class="payment-type-title">
                                            Filiale
                                        </h2>
                                        <p>
                                            Cassino  <a class="material-icons-outlined-14 action external-link-icon" style="" href="/BranchOffice/Details/1" title="Apri in una nuova finestra" target="_blank">open_in_new</a><span class="action external-link-desc"></span>
                                        </p>
                                    </div>
                                </div>
                            <br />
                                <div class="row">
                                    <div class="col-xs-8">
                                        <div class="payment-type-title">
                                            <h2 class="payment-type-title"> Fatturato a </h2>
                                            <div class="payment-type-line">
                                                Tomas Rently
                                                <a class="material-icons-outlined-14 action external-link-icon" style=" " href="/Customer/Details/3" title="Apri in una nuova finestra" target="_blank">open_in_new</a><span class="action external-link-desc"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                        </div>
                    
                </div>
                <div id="comments" class="tab-pane fade">
                    <div class="loader"></div>
                    
                </div>
                <div id="items" class="tab-pane fade">

<div id="itemsGrid" class="mvc-grid">
    <table class="table">
        <thead>
            <tr>
                    <th
                        data-name="Description"
                        data-filter=""
                        data-filter-name="Text"
                        data-filter-multi=""
                        data-filter-first-type=""
                        data-filter-first-val=""
                        data-filter-operator=""
                        data-filter-second-type=""
                        data-filter-second-val=""
                        data-sort=""
                        data-sort-order=""
                        data-sort-first="">
                            <span class="mvc-grid-header-title">Descrizione</span>
                                            </th>
                    <th
                        data-name="Type"
                        data-filter=""
                        data-filter-name=""
                        data-filter-multi=""
                        data-filter-first-type=""
                        data-filter-first-val=""
                        data-filter-operator=""
                        data-filter-second-type=""
                        data-filter-second-val=""
                        data-sort=""
                        data-sort-order=""
                        data-sort-first="">
                            <span class="mvc-grid-header-title">Tipo</span>
                                            </th>
                    <th
                        data-name="Quantity"
                        data-filter=""
                        data-filter-name="Number"
                        data-filter-multi=""
                        data-filter-first-type=""
                        data-filter-first-val=""
                        data-filter-operator=""
                        data-filter-second-type=""
                        data-filter-second-val=""
                        data-sort=""
                        data-sort-order=""
                        data-sort-first="">
                            <span class="mvc-grid-header-title">Quantità</span>
                                            </th>
                    <th
                        data-name=""
                        data-filter="False"
                        data-filter-name="Text"
                        data-filter-multi=""
                        data-filter-first-type=""
                        data-filter-first-val=""
                        data-filter-operator=""
                        data-filter-second-type=""
                        data-filter-second-val=""
                        data-sort="False"
                        data-sort-order=""
                        data-sort-first="">
                            <span class="mvc-grid-header-title">Prezzo unitario</span>
                                            </th>
                    <th
                        data-name=""
                        data-filter="False"
                        data-filter-name="Text"
                        data-filter-multi=""
                        data-filter-first-type=""
                        data-filter-first-val=""
                        data-filter-operator=""
                        data-filter-second-type=""
                        data-filter-second-val=""
                        data-sort="False"
                        data-sort-order=""
                        data-sort-first="">
                            <span class="mvc-grid-header-title">Prezzo</span>
                                            </th>
            </tr>
        </thead>
        <tbody>
                    <tr>
                            <td>Booking #1 (GP213RB (GP213RB) - Fiat Tipo SW Automatic or similar (Blue) Station Wagon Automatic (group S1)) since 01/10/2024 until 05/10/2024</td>
                            <td>Prenotazione</td>
                            <td>1</td>
                            <td>€ 1.080,00</td>
                            <td>€ 1.306,80</td>
                    </tr>
        </tbody>
    </table>
</div>
                </div>
                <div id="erpDetail" class="tab-pane fade">



                </div>
                <div id="erpTaxes" class="tab-pane fade">

                </div>
                <div id="files" class="tab-pane fade">

                        <div class="row">
                            <div id="filesPanel">
                                <div class="panel-heading">
                                    <div class="float-right">
                                        <a href="javascript:uploadInvoiceFiles(3, fileUploaded);">
                                            <span class="material-icons mr-10" title="Aggiungi File">upload</span>
                                        </a>
                                        <a href="javascript:captureImageToSave(3, 'InvoiceId', fileUploaded)">
                                            <span class="material-icons">add_a_photo</span>
                                        </a>
                                        <br />
                                        <span class="upload-file">Aggiungi File</span>
                                    </div>
                                </div>
                                <div class="panel-body" id="filePanel">

                                </div>
                            </div>
                        </div>
                </div>
            </div>
        </div>
    </div>


        <div class="col-lg-4">
            <div class="panel">
                <div class='bookingTooltip panel-body'>
                    <div class='row'>
                        <div class='col-md-8'>
                                <span style="font-weight:normal;">Fatturato a</span>
                                <span style="font-weight:normal;">Cliente</span>
                                <h2 style="margin-top: 0px !important; margin-bottom: 10px !important;" title='Cliente'>Tomas Rently</h2>
                                <div class="payment-type-line">
                                    Tomas Rently
                                    <a class="material-icons-outlined-14 action external-link-icon" style=" " href="/Customer/Details/3" title="Apri in una nuova finestra" target="_blank">open_in_new</a><span class="action external-link-desc"></span>
                                </div>
                        </div>
                    </div>
                        <div class="row">
                            <div class='bookingTooltip panel-body'>
                                <div class='row'>
                                    <div class='col-md-4'>

                                            <span style="font-weight:normal;">Agenzia</span>
                                            <h4 style="margin-top: 0px !important; margin-bottom: 10px !important;" title='Agenzia'>Agency Test</h4>

                                        <span class='booking-id'><span style="font-weight:normal;">Prenotazione</span> #1 <a class="material-icons-outlined-14 action" title="Apri in una nuova finestra" href="/Booking/Details/1" target="_blank">open_in_new</a></span>
                                        <span class='fa fa-circle statusClosed'></span><span class='booking-status-text'>Terminata</span>
                                        <div class='balance-info visible'>
                                            <span class='debt-title'>Debito</span>
                                            <span class='balance'>€ 1.306,80</span>
                                        </div>
                                        <div class='payed ' title="€ 10.000,38">
                                            <span class='fa fa-check-circle'></span> <span>Pagato</span>
                                        </div>
                                    </div>
                                    <div class='col-md-1'>
                                        <div class='divider'></div>
                                    </div>
                                    <div class='col-md-7 tooltipInfo'>
                                        <div class='booking-info'>
                                            <div class='info-icon'><span class='material-icons-outlined'>directions_car</span></div>
                                            <div class='info-description'>
                                                <span class='info'>GP213RB-Tipo SW Automatica o similare</span>
                                                <span class='info-place'>Station Wagon Automatica (gruppo S1)</span>
                                            </div>
                                        </div>
                                        <div class='booking-info'>
                                            <div class='info-icon'><span class='material-icons-outlined forward'>arrow_forward</span></div>
                                            <div class='info-description'>
                                                <span class='info'>01/10/2024 11:00:00</span>
                                                <span class='info-place'>Cassino </span>
                                            </div>
                                        </div>
                                        <div class='booking-info'>
                                            <div class='info-icon'><span class='material-icons-outlined back'>arrow_back</span></div>
                                            <div class='info-description'>
                                                <span class='info'>05/10/2024 11:00:00</span>
                                                <span class='info-place'>Cassino </span>
                                            </div>
                                        </div>
                                        <div class='booking-info'>
                                            <div class='info-icon'><span class='material-icons-outlined'>schedule</span></div>
                                            <div class='info-description'>
                                                <span class='info'>4 Giorni</span>
                                            </div>
                                        </div>
                                        <div class='booking-info float-right'>
                                            <div class='additionals'>
                                            </div>
                                            <div class='creator'>
                                                <span class='origin-title'>Origine</span>
                                                <span class='origin-description'>Social Media</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                </div>
            </div>
        </div>
    <div class="col-lg-4">

            </div>
</div>

<style>
    .tooltipInfo {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .booking-info.float-right {
        margin-top: auto;
        align-self: flex-end;
    }
</style>

<script type="text/javascript" nonce="c74506b51d8845f4903918c1ff3c8879">
    var fileUploader = null;

    function fileUploaded() {
        $("#filePanel").fileViewer("reload");
    }

    function showFiles(id) {
			$("<div/>").fileViewer({
				url:"/File/FileViewer",
				params:"invoiceId=" + id
			});
		}

    $(document).ready(function () {

        // Add minus icon for collapse element which is open by default
        $(".collapse.in").each(function () {
            $(this).siblings(".panel-heading").find(".fa").addClass("fa-chevron-up").removeClass("fa-chevron-down");
        });

        // Toggle plus minus icon on show hide of collapse element
        $(".collapse").on('show.bs.collapse', function () {
            $(this).parent().find(".fa").removeClass("fa-chevron-down").addClass("fa-chevron-up");
        }).on('hide.bs.collapse', function () {
            $(this).parent().find(".fa").removeClass("fa-chevron-up").addClass("fa-chevron-down");
        });

        PaymentContextMenu({
            selector: 'i.menu-item.payment',
        });

        fileUploaderOptions = {
            useModal: true,
            language: {
                name: 'Nome',
                fileType: 'Tipo di File',
                modalTitle: 'Carica',
                save: 'Salvare',
                cancel: 'Annullare',
                sectionMessageText: 'Trascina o clicca qui per allegare i tuoi file',
                sectionDropFile: 'Trascina o clicca qui per allegare i tuoi file'
            },
            documentType: 'Invoice',
            fileTypeEnum: 22,
            customHiddenInput: {
                name: 'InvoiceId',
                value: '3'
            },
            sendModalFormCallBack: function (response) {
                if (response.Success) {
                    showWaitDialog(GlobalVariables.Resources.LoadingFile).changeDialogMessage({
                        callback: fileUploaded,
                        message: GlobalVariables.Resources.FileUploadSuccess
                    });
                } else {
                    showWaitDialog(GlobalVariables.Resources.LoadingFile).changeDialogMessage({
                        type: "error",
                        message: response.Data
                    });
                };
            }
        };

        fileUploader = $("#page-title").fileUploader(fileUploaderOptions);
        $("#filePanel").fileUploader(fileUploaderOptions);

        $("#filePanel").fileViewer({
			url:"/File/FileViewer",
			params:"invoiceId=3",
			itemsPerPage:10,
			showDialog: false
		});

		initLazyGrids();

    });
</script>

		</section>
	</div>
	
<div class="footer">
	<div class="content-wrapper">
		<div class="float-left">
				<img src="/Images/logo_2022.png" class="logo" />
			<span style="vertical-align:middle;">&copy; 2025</span>
		</div>

		<div class="float-right app-version">
			<p> <strong>Rently</strong><span style="font-style:italic"> v4.3.0.250</span></p>
		</div>
		<div class="float-right separator app-version">
			|
		</div>
		<div class="float-right" title="W. Europe Standard Time">
			02/09/2025 17:34
		</div>
	</div>
</div>

			<script type="text/javascript" id="hs-script-loader" async defer src="//js-na1.hs-scripts.com/9033403.js"></script>
		<script type="text/javascript" nonce="c74506b51d8845f4903918c1ff3c8879">
			function onConversationsAPIReady() {
				var tokenStr = localStorage.getItem('hubspot_chat_token_vivarent_16');
				var tokenInfo = tokenStr != null ? JSON.parse(tokenStr) : null;

				if (tokenInfo == null || tokenInfo.expiresOn < new Date().getTime()) {
					//get token
					$.post('/Home/HubSpotChatToken', null, (res) => {
						localStorage.setItem('hubspot_chat_token_vivarent_16', JSON.stringify({ token: res.token, expiresOn: new Date().addHours(11).getTime() }));

						setConversationSettings(res.token);
					});
				} else {
					setConversationSettings(tokenInfo.token);
				}
			}

			function setConversationSettings(token) {
				window.hsConversationsSettings = {
					identificationEmail: '<EMAIL>',
					identificationToken: token,
				};

				window.HubSpotConversations.widget.load();
			}

			window.hsConversationsSettings = {
				loadImmediately: false,
			};

			if (window.HubSpotConversations) {
				onConversationsAPIReady();
			} else {
				window.hsConversationsOnReady = [onConversationsAPIReady];
			}
		</script>

<style>
	#hubspot-messages-iframe-container.widget-align-right {
		bottom: 40px !important;
	}

	@media (max-width: 768px) {
		#hubspot-messages-iframe-container.widget-align-right{
			bottom: 60px !important;
			max-height: 90% !important;
		}
	}
</style>
	<script type="text/javascript" nonce="c74506b51d8845f4903918c1ff3c8879">
        $(document).ready(function () {
            $(".sub-menu li").tooltip({
                create: function (ev, ui) { $(this).data("ui-tooltip").liveRegion.remove(); }
            }
            )
        });

        $(window).keydown(function (e) {
            $(".sub-menu li").each(function (i, element) {
                var data = $(element).data();
                if (data.shortcutCtrl == e.ctrlKey && e.key && e.key.toLowerCase() == data.shortcutKey) {
                    window.location.href = $(element).find("a").attr("href");
                    e.preventDefault();
                    return false;
                }
            });
        });

        $(document).ready(function () {
            //$(document).tooltip(
            //    {
            //        create: function (ev, ui) { $(this).data("ui-tooltip").liveRegion.remove(); }
            //    });
            $("i,image,p").tooltip(
                {
                    create: function (ev, ui) { $(this).data("ui-tooltip").liveRegion.remove(); }
                }
            );

			if (typeof kendo !== 'undefined' && kendo)
                kendo.culture("it");

        });
	</script>

	<script type="text/javascript" nonce="c74506b51d8845f4903918c1ff3c8879">
        $("#body").keydown(function (event) {
            if (isKeyCodeCtrlQ(event)) {
                window.open(GlobalVariables.Urls.LoadCustomJs + '?url=' + "/invoice/details", '_blank');
            }
        });

        
	</script>
</body>
</html>
