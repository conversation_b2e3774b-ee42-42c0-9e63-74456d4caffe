"""
Configuration file for Rently Invoice Downloader
"""

# Default settings
DEFAULT_CONFIG = {
    'base_url': 'https://vivarent.rently.com.ar',
    'output_dir': 'downloads',
    'delay_between_requests': 1.0,
    'max_retries': 3,
    'timeout': 30,
    'chunk_size': 8192,
}

# PDF detection patterns
PDF_DETECTION_PATTERNS = {
    'url_patterns': [
        r'.*\.pdf$',
        r'.*pdf.*',
        r'.*download.*',
        r'.*print.*',
        r'.*export.*'
    ],
    'text_patterns': [
        'download',
        'descargar', 
        'pdf',
        'print',
        'imprimir',
        'export',
        'exportar'
    ],
    'content_type_patterns': [
        'application/pdf',
        'application/octet-stream'
    ]
}

# Common invoice page selectors (CSS/XPath patterns to look for)
INVOICE_SELECTORS = {
    'pdf_links': [
        'a[href*="pdf"]',
        'a[href*="download"]',
        'a[href*="print"]',
        '.download-btn',
        '.pdf-download',
        'button[onclick*="pdf"]',
        'button[onclick*="download"]'
    ],
    'invoice_info': [
        '.invoice-number',
        '.invoice-date',
        '.invoice-amount',
        '#invoice-details'
    ]
}

# Headers to use for requests
REQUEST_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}
