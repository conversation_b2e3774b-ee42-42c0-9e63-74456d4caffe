#!/usr/bin/env python3
"""
Invoice Analyzer - Test and analyze individual invoice pages

This utility helps analyze the structure of individual invoice pages
to understand how PDF downloads work before running bulk downloads.

Usage:
    python invoice_analyzer.py --username YOUR_USERNAME --password YOUR_PASSWORD --invoice-id 3
"""

import requests
import argparse
import sys
import json
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import logging
from rently_invoice_downloader import RentlyInvoiceDownloader
from config import PDF_DETECTION_PATTERNS, INVOICE_SELECTORS

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class InvoiceAnalyzer(RentlyInvoiceDownloader):
    def __init__(self, base_url="https://vivarent.rently.com.ar"):
        super().__init__(base_url)
    
    def analyze_invoice_page(self, invoice_id, save_html=False):
        """Analyze an invoice page and extract all relevant information"""
        if not self.authenticated:
            logger.error("Not authenticated. Please login first.")
            return None
        
        invoice_html = self.get_invoice_details(invoice_id)
        if not invoice_html:
            return None
        
        # Save HTML for manual inspection if requested
        if save_html:
            with open(f'invoice_{invoice_id}_page.html', 'w', encoding='utf-8') as f:
                f.write(invoice_html)
            logger.info(f"Saved HTML to invoice_{invoice_id}_page.html")
        
        soup = BeautifulSoup(invoice_html, 'html.parser')
        
        analysis = {
            'invoice_id': invoice_id,
            'page_title': soup.title.string if soup.title else 'No title',
            'forms': [],
            'links': [],
            'buttons': [],
            'scripts': [],
            'potential_pdf_urls': [],
            'invoice_info': {}
        }
        
        # Analyze forms
        for form in soup.find_all('form'):
            form_info = {
                'action': form.get('action', ''),
                'method': form.get('method', 'GET'),
                'inputs': []
            }
            
            for input_elem in form.find_all('input'):
                form_info['inputs'].append({
                    'name': input_elem.get('name', ''),
                    'type': input_elem.get('type', ''),
                    'value': input_elem.get('value', '')
                })
            
            analysis['forms'].append(form_info)
        
        # Analyze links
        for link in soup.find_all('a', href=True):
            href = link.get('href')
            text = link.get_text().strip()
            
            link_info = {
                'href': href,
                'text': text,
                'full_url': urljoin(self.base_url, href) if href else '',
                'is_potential_pdf': self._is_potential_pdf_link(href, text)
            }
            
            analysis['links'].append(link_info)
            
            if link_info['is_potential_pdf']:
                analysis['potential_pdf_urls'].append(link_info['full_url'])
        
        # Analyze buttons
        for button in soup.find_all('button'):
            onclick = button.get('onclick', '')
            text = button.get_text().strip()
            
            button_info = {
                'text': text,
                'onclick': onclick,
                'type': button.get('type', ''),
                'is_potential_pdf': self._is_potential_pdf_button(onclick, text)
            }
            
            analysis['buttons'].append(button_info)
        
        # Analyze scripts for PDF-related JavaScript
        for script in soup.find_all('script'):
            if script.string:
                script_content = script.string.strip()
                if any(keyword in script_content.lower() for keyword in ['pdf', 'download', 'print']):
                    analysis['scripts'].append({
                        'content_preview': script_content[:200] + '...' if len(script_content) > 200 else script_content,
                        'has_pdf_keywords': True
                    })
        
        # Try to extract invoice information
        analysis['invoice_info'] = self._extract_invoice_info(soup)
        
        return analysis
    
    def _is_potential_pdf_link(self, href, text):
        """Check if a link might be a PDF download"""
        if not href and not text:
            return False
        
        href_lower = href.lower() if href else ''
        text_lower = text.lower() if text else ''
        
        # Check URL patterns
        for pattern in PDF_DETECTION_PATTERNS['url_patterns']:
            if pattern.replace('.*', '') in href_lower:
                return True
        
        # Check text patterns
        for pattern in PDF_DETECTION_PATTERNS['text_patterns']:
            if pattern in text_lower:
                return True
        
        return False
    
    def _is_potential_pdf_button(self, onclick, text):
        """Check if a button might trigger PDF download"""
        onclick_lower = onclick.lower() if onclick else ''
        text_lower = text.lower() if text else ''
        
        pdf_keywords = ['pdf', 'download', 'print', 'export']
        
        return any(keyword in onclick_lower or keyword in text_lower for keyword in pdf_keywords)
    
    def _extract_invoice_info(self, soup):
        """Extract invoice-specific information from the page"""
        info = {}
        
        # Look for common invoice information patterns
        patterns = {
            'invoice_number': ['invoice-number', 'invoice_number', 'numero-factura'],
            'invoice_date': ['invoice-date', 'invoice_date', 'fecha-factura'],
            'amount': ['amount', 'total', 'importe'],
            'customer': ['customer', 'cliente', 'tenant']
        }
        
        for info_type, selectors in patterns.items():
            for selector in selectors:
                # Try class selectors
                elem = soup.find(class_=selector)
                if not elem:
                    # Try ID selectors
                    elem = soup.find(id=selector)
                if not elem:
                    # Try data attribute selectors
                    elem = soup.find(attrs={'data-field': selector})
                
                if elem:
                    info[info_type] = elem.get_text().strip()
                    break
        
        return info
    
    def test_pdf_download(self, invoice_id, pdf_url):
        """Test downloading a specific PDF URL"""
        logger.info(f"Testing PDF download for invoice {invoice_id} from: {pdf_url}")
        
        try:
            response = self.session.head(pdf_url)  # Use HEAD to check without downloading
            response.raise_for_status()
            
            content_type = response.headers.get('content-type', '')
            content_length = response.headers.get('content-length', 'Unknown')
            
            logger.info(f"Response headers:")
            logger.info(f"  Content-Type: {content_type}")
            logger.info(f"  Content-Length: {content_length}")
            
            # If HEAD request looks good, try a small download
            if 'pdf' in content_type.lower() or content_length != 'Unknown':
                response = self.session.get(pdf_url, stream=True)
                first_chunk = next(response.iter_content(chunk_size=1024), b'')
                
                if first_chunk.startswith(b'%PDF'):
                    logger.info("✓ Response appears to be a valid PDF")
                    return True
                else:
                    logger.warning("✗ Response doesn't appear to be a PDF")
                    logger.info(f"First bytes: {first_chunk[:50]}")
                    return False
            else:
                logger.warning("✗ Content-Type doesn't indicate PDF")
                return False
                
        except requests.RequestException as e:
            logger.error(f"✗ Error testing PDF download: {e}")
            return False


def main():
    parser = argparse.ArgumentParser(description='Analyze individual invoice pages')
    parser.add_argument('--username', required=True, help='Rently username')
    parser.add_argument('--password', required=True, help='Rently password')
    parser.add_argument('--invoice-id', type=int, required=True, help='Invoice ID to analyze')
    parser.add_argument('--save-html', action='store_true', help='Save HTML page for manual inspection')
    parser.add_argument('--test-downloads', action='store_true', help='Test PDF download URLs')
    parser.add_argument('--output-json', help='Save analysis results to JSON file')
    parser.add_argument('--base-url', default='https://vivarent.rently.com.ar', help='Base URL of Rently instance')
    
    args = parser.parse_args()
    
    # Create analyzer instance
    analyzer = InvoiceAnalyzer(args.base_url)
    
    # Authenticate
    if not analyzer.login(args.username, args.password):
        logger.error("Authentication failed. Please check your credentials.")
        sys.exit(1)
    
    # Analyze the invoice page
    analysis = analyzer.analyze_invoice_page(args.invoice_id, args.save_html)
    
    if not analysis:
        logger.error(f"Failed to analyze invoice {args.invoice_id}")
        sys.exit(1)
    
    # Print analysis results
    print(f"\n=== Analysis Results for Invoice {args.invoice_id} ===")
    print(f"Page Title: {analysis['page_title']}")
    
    if analysis['invoice_info']:
        print(f"\nInvoice Information:")
        for key, value in analysis['invoice_info'].items():
            print(f"  {key}: {value}")
    
    print(f"\nForms found: {len(analysis['forms'])}")
    for i, form in enumerate(analysis['forms']):
        print(f"  Form {i+1}: {form['method']} {form['action']}")
        print(f"    Inputs: {len(form['inputs'])}")
    
    print(f"\nLinks found: {len(analysis['links'])}")
    pdf_links = [link for link in analysis['links'] if link['is_potential_pdf']]
    print(f"Potential PDF links: {len(pdf_links)}")
    
    for link in pdf_links:
        print(f"  • {link['text']} -> {link['full_url']}")
    
    print(f"\nButtons found: {len(analysis['buttons'])}")
    pdf_buttons = [btn for btn in analysis['buttons'] if btn['is_potential_pdf']]
    print(f"Potential PDF buttons: {len(pdf_buttons)}")
    
    for button in pdf_buttons:
        print(f"  • {button['text']} (onclick: {button['onclick'][:50]}...)")
    
    print(f"\nScripts with PDF keywords: {len(analysis['scripts'])}")
    
    # Test PDF downloads if requested
    if args.test_downloads and analysis['potential_pdf_urls']:
        print(f"\n=== Testing PDF Downloads ===")
        for url in analysis['potential_pdf_urls']:
            analyzer.test_pdf_download(args.invoice_id, url)
    
    # Save to JSON if requested
    if args.output_json:
        with open(args.output_json, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, indent=2, ensure_ascii=False)
        print(f"\nAnalysis saved to {args.output_json}")


if __name__ == "__main__":
    main()
