# Rently Invoice PDF Downloader

A Python-based solution for bulk downloading PDF invoices from the Rently platform using HTTP requests (no browser automation required).

## Features

- **Authentication handling**: Automatically handles login with username/password
- **Bulk downloads**: Download multiple invoices by specifying ID ranges
- **Smart PDF detection**: Multiple strategies to find PDF download URLs
- **Session management**: Maintains login session across requests
- **Error handling**: Robust error handling with retry logic
- **Logging**: Comprehensive logging for debugging and monitoring
- **Analysis tools**: Utilities to analyze individual invoice pages
- **Configurable**: Easy to customize for different Rently instances

## Installation

1. Clone or download this repository
2. Install Python dependencies:

```bash
pip install -r requirements.txt
```

## Quick Start

### 1. Analyze a Single Invoice (Recommended First Step)

Before running bulk downloads, analyze a single invoice to understand the page structure:

```bash
python invoice_analyzer.py --username YOUR_USERNAME --password YOUR_PASSWORD --invoice-id 3 --save-html --test-downloads
```

This will:
- Login to the Rently platform
- Analyze the invoice page structure
- Save the HTML for manual inspection
- Test potential PDF download URLs
- Show detailed analysis results

### 2. Download Invoice Range

Once you understand the structure, download multiple invoices:

```bash
python rently_invoice_downloader.py --username YOUR_USERNAME --password YOUR_PASSWORD --start-id 3 --end-id 20 --output-dir downloads
```

## Usage Examples

### Basic Download
```bash
python rently_invoice_downloader.py --username myuser --password mypass --start-id 1 --end-id 10
```

### Custom Configuration
```bash
python rently_invoice_downloader.py \
  --username myuser \
  --password mypass \
  --start-id 100 \
  --end-id 200 \
  --output-dir /path/to/downloads \
  --delay 2.0 \
  --base-url https://your-rently-instance.com
```

### Analyze Specific Invoice
```bash
python invoice_analyzer.py \
  --username myuser \
  --password mypass \
  --invoice-id 42 \
  --save-html \
  --test-downloads \
  --output-json analysis_results.json
```

## Command Line Options

### rently_invoice_downloader.py

| Option | Description | Default |
|--------|-------------|---------|
| `--username` | Rently username (required) | - |
| `--password` | Rently password (required) | - |
| `--start-id` | Starting invoice ID | 3 |
| `--end-id` | Ending invoice ID | 10 |
| `--output-dir` | Output directory for PDFs | downloads |
| `--delay` | Delay between requests (seconds) | 1.0 |
| `--base-url` | Base URL of Rently instance | https://vivarent.rently.com.ar |

### invoice_analyzer.py

| Option | Description | Default |
|--------|-------------|---------|
| `--username` | Rently username (required) | - |
| `--password` | Rently password (required) | - |
| `--invoice-id` | Invoice ID to analyze (required) | - |
| `--save-html` | Save HTML page for inspection | False |
| `--test-downloads` | Test PDF download URLs | False |
| `--output-json` | Save analysis to JSON file | - |
| `--base-url` | Base URL of Rently instance | https://vivarent.rently.com.ar |

## How It Works

### Authentication Process
1. Fetches the login page to extract anti-forgery token
2. Submits login form with credentials and token
3. Maintains session cookies for subsequent requests

### PDF Detection Strategies
The downloader uses multiple strategies to find PDF download URLs:

1. **Direct PDF links**: Links with `.pdf` extension or `pdf` in URL
2. **Download buttons**: Links/buttons with download-related text
3. **Form submissions**: Forms that might generate PDFs
4. **JavaScript analysis**: Extracts URLs from PDF-related JavaScript

### File Naming
Downloaded PDFs are saved with meaningful names:
```
invoice_{invoice_id}_{timestamp}.pdf
```
Example: `invoice_42_20231201_143022.pdf`

## Configuration

Edit `config.py` to customize:
- PDF detection patterns
- Request headers
- Timeout settings
- CSS selectors for invoice information

## Troubleshooting

### Common Issues

1. **Authentication fails**
   - Verify username and password
   - Check if the Rently instance URL is correct
   - Ensure account has access to invoices

2. **No PDF URLs found**
   - Run the analyzer first: `python invoice_analyzer.py --invoice-id X --save-html`
   - Check the saved HTML file manually
   - Update PDF detection patterns in `config.py`

3. **Downloads fail**
   - Check network connectivity
   - Verify invoice IDs exist
   - Increase delay between requests
   - Check logs for detailed error messages

### Debugging

1. **Enable verbose logging**:
   ```python
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **Save HTML pages**:
   ```bash
   python invoice_analyzer.py --invoice-id X --save-html
   ```

3. **Check log files**:
   - `rently_downloader.log` contains detailed execution logs

## Security Notes

- Store credentials securely (consider environment variables)
- Use appropriate delays between requests to avoid overwhelming the server
- Respect the platform's terms of service
- Consider using a VPN if accessing from different geographic locations

## Extending the Solution

### Adding New PDF Detection Patterns

Edit `config.py` and add patterns to `PDF_DETECTION_PATTERNS`:

```python
PDF_DETECTION_PATTERNS = {
    'url_patterns': [
        r'.*\.pdf$',
        r'.*your_custom_pattern.*'
    ],
    'text_patterns': [
        'download',
        'your_custom_text'
    ]
}
```

### Supporting Different Rently Instances

The solution is designed to work with different Rently instances. Simply change the `--base-url` parameter:

```bash
python rently_invoice_downloader.py --base-url https://your-instance.rently.com
```

## Dependencies

- `requests`: HTTP library for making web requests
- `beautifulsoup4`: HTML parsing library
- `lxml`: XML/HTML parser (faster than default parser)

## License

This project is provided as-is for educational and legitimate business purposes. Ensure compliance with the Rently platform's terms of service.

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review log files for detailed error messages
3. Use the analyzer tool to understand page structure
4. Ensure you have proper access permissions to the invoices
